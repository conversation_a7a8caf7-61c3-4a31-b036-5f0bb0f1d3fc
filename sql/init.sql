-- YangTravel数据库初始化脚本
-- 数据库：yangyangchuyou
-- 创建时间：2025-08-08

-- 创建用户信息表
CREATE TABLE IF NOT EXISTS `user_info` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `nickname` VARCHAR(100) DEFAULT NULL COMMENT '昵称',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `gender` TINYINT(1) DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status` TINYINT(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_phone` (`phone`),
    KEY `idx_email` (`email`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 创建管理员表
CREATE TABLE IF NOT EXISTS `admin` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(100) NOT NULL COMMENT '密码',
    `nickname` VARCHAR(100) DEFAULT NULL COMMENT '昵称',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status` TINYINT(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 插入默认管理员账号
INSERT INTO `admin` (`username`, `password`, `nickname`, `status`) VALUES 
('admin', '1234@qwer', '系统管理员', 1)
ON DUPLICATE KEY UPDATE 
`password` = VALUES(`password`),
`nickname` = VALUES(`nickname`),
`status` = VALUES(`status`);

-- 插入测试用户数据
INSERT INTO `user_info` (`username`, `nickname`, `phone`, `email`, `gender`, `status`) VALUES 
('testuser1', '测试用户1', '13800138001', '<EMAIL>', 1, 1),
('testuser2', '测试用户2', '13800138002', '<EMAIL>', 2, 1),
('testuser3', '测试用户3', '13800138003', '<EMAIL>', 0, 0),
('testuser4', '测试用户4', '13800138004', '<EMAIL>', 1, 1),
('testuser5', '测试用户5', '13800138005', '<EMAIL>', 2, 1)
ON DUPLICATE KEY UPDATE 
`nickname` = VALUES(`nickname`),
`phone` = VALUES(`phone`),
`email` = VALUES(`email`),
`gender` = VALUES(`gender`),
`status` = VALUES(`status`);

-- 创建索引优化查询性能
-- 用户信息表索引
CREATE INDEX IF NOT EXISTS `idx_user_info_username_status` ON `user_info` (`username`, `status`);
CREATE INDEX IF NOT EXISTS `idx_user_info_create_time_status` ON `user_info` (`create_time`, `status`);

-- 管理员表索引
CREATE INDEX IF NOT EXISTS `idx_admin_username_status` ON `admin` (`username`, `status`);

-- 查看表结构和数据
SELECT 'user_info表结构' as info;
DESCRIBE `user_info`;

SELECT 'admin表结构' as info;
DESCRIBE `admin`;

SELECT 'user_info表数据' as info;
SELECT * FROM `user_info` LIMIT 10;

SELECT 'admin表数据' as info;
SELECT `id`, `username`, `nickname`, `create_time`, `status` FROM `admin` LIMIT 10;
