#!/bin/bash

echo "========================================"
echo "YangTravel 微服务启动脚本"
echo "========================================"

echo ""
echo "正在编译项目..."
mvn clean package -DskipTests

if [ $? -ne 0 ]; then
    echo "编译失败，请检查错误信息"
    exit 1
fi

echo ""
echo "编译成功！"
echo ""

echo "启动用户服务 (端口: 8081)..."
cd user-service
nohup mvn spring-boot:run > ../user-service.log 2>&1 &
USER_PID=$!
cd ..

echo "等待3秒..."
sleep 3

echo "启动管理服务 (端口: 8082)..."
cd admin-service
nohup mvn spring-boot:run > ../admin-service.log 2>&1 &
ADMIN_PID=$!
cd ..

echo ""
echo "========================================"
echo "服务启动完成！"
echo ""
echo "用户服务: http://localhost:8081"
echo "用户服务API文档: http://localhost:8081/swagger-ui.html"
echo ""
echo "管理服务: http://localhost:8082"
echo "管理服务API文档: http://localhost:8082/swagger-ui.html"
echo ""
echo "管理员账号: admin / 1234@qwer"
echo ""
echo "用户服务PID: $USER_PID"
echo "管理服务PID: $ADMIN_PID"
echo ""
echo "日志文件:"
echo "  用户服务: user-service.log"
echo "  管理服务: admin-service.log"
echo ""
echo "停止服务: kill $USER_PID $ADMIN_PID"
echo "========================================"
