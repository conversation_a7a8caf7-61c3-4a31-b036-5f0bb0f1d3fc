package com.yangtravel.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yangtravel.user.entity.UserInfo;

/**
 * 用户信息服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface UserInfoService extends IService<UserInfo> {

    /**
     * 分页查询用户列表
     * 
     * @param page 分页参数
     * @param username 用户名（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    IPage<UserInfo> getUserPage(Page<UserInfo> page, String username, Integer status);

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    UserInfo getUserByUsername(String username);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeId);
}
