package com.yangtravel.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yangtravel.user.entity.UserInfo;
import com.yangtravel.user.mapper.UserInfoMapper;
import com.yangtravel.user.service.UserInfoService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 用户信息服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    @Override
    public IPage<UserInfo> getUserPage(Page<UserInfo> page, String username, Integer status) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据用户名模糊查询
        if (StringUtils.hasText(username)) {
            queryWrapper.like(UserInfo::getUsername, username);
        }
        
        // 根据状态查询
        if (status != null) {
            queryWrapper.eq(UserInfo::getStatus, status);
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(UserInfo::getCreateTime);
        
        return this.page(page, queryWrapper);
    }

    @Override
    public UserInfo getUserByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }
        
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUsername, username);
        
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean isUsernameExists(String username, Long excludeId) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUsername, username);
        
        // 排除指定ID（用于更新时检查）
        if (excludeId != null) {
            queryWrapper.ne(UserInfo::getId, excludeId);
        }
        
        return this.count(queryWrapper) > 0;
    }
}
