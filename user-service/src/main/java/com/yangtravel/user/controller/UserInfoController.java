package com.yangtravel.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yangtravel.user.common.Result;
import com.yangtravel.user.entity.UserInfo;
import com.yangtravel.user.exception.BusinessException;
import com.yangtravel.user.service.UserInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户信息控制器
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@RestController
@RequestMapping("/api/users")
@Tag(name = "用户管理", description = "用户信息的CRUD操作")
public class UserInfoController {

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 查询用户列表（分页）
     */
    @GetMapping
    @Operation(summary = "查询用户列表", description = "支持分页和条件查询")
    public Result<IPage<UserInfo>> getUserList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam(required = false) Integer status) {
        
        Page<UserInfo> page = new Page<>(current, size);
        IPage<UserInfo> result = userInfoService.getUserPage(page, username, status);
        return Result.success(result);
    }

    /**
     * 根据ID查询用户
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询用户", description = "根据用户ID查询用户详细信息")
    public Result<UserInfo> getUserById(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        
        UserInfo userInfo = userInfoService.getById(id);
        if (userInfo == null) {
            throw new BusinessException(404, "用户不存在");
        }
        return Result.success(userInfo);
    }

    /**
     * 新增用户
     */
    @PostMapping
    @Operation(summary = "新增用户", description = "创建新的用户信息")
    public Result<UserInfo> createUser(@Valid @RequestBody UserInfo userInfo) {
        
        // 检查用户名是否已存在
        if (userInfoService.isUsernameExists(userInfo.getUsername(), null)) {
            throw new BusinessException(400, "用户名已存在");
        }
        
        // 设置默认值
        if (userInfo.getStatus() == null) {
            userInfo.setStatus(1); // 默认启用
        }
        if (userInfo.getGender() == null) {
            userInfo.setGender(0); // 默认未知
        }
        
        boolean success = userInfoService.save(userInfo);
        if (!success) {
            throw new BusinessException("用户创建失败");
        }
        
        return Result.success("用户创建成功", userInfo);
    }

    /**
     * 修改用户信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "修改用户信息", description = "根据用户ID修改用户信息")
    public Result<UserInfo> updateUser(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id,
            @Valid @RequestBody UserInfo userInfo) {
        
        // 检查用户是否存在
        UserInfo existingUser = userInfoService.getById(id);
        if (existingUser == null) {
            throw new BusinessException(404, "用户不存在");
        }
        
        // 检查用户名是否已被其他用户使用
        if (userInfoService.isUsernameExists(userInfo.getUsername(), id)) {
            throw new BusinessException(400, "用户名已被其他用户使用");
        }
        
        userInfo.setId(id);
        boolean success = userInfoService.updateById(userInfo);
        if (!success) {
            throw new BusinessException("用户更新失败");
        }
        
        return Result.success("用户更新成功", userInfo);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "根据用户ID删除用户")
    public Result<Void> deleteUser(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        
        // 检查用户是否存在
        UserInfo existingUser = userInfoService.getById(id);
        if (existingUser == null) {
            throw new BusinessException(404, "用户不存在");
        }
        
        boolean success = userInfoService.removeById(id);
        if (!success) {
            throw new BusinessException("用户删除失败");
        }
        
        return Result.success();
    }
}
