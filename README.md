# YangTravel 微服务项目

## 项目简介

YangTravel是一个基于Spring Boot 3.2.x的微服务项目，采用Maven多模块架构，提供用户管理和后台管理功能。

## 技术栈

- **编程语言**: Java 17
- **框架**: Spring Boot 3.2.5
- **构建工具**: Maven
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis Plus 3.5.5
- **API文档**: SpringDoc OpenAPI 3.0
- **工具类**: Hutool 5.8.25

## 项目结构

```
YangTravel/
├── pom.xml                    # 父模块POM文件
├── user-service/             # 用户服务模块
│   ├── src/main/java/
│   │   └── com/yangtravel/user/
│   │       ├── UserServiceApplication.java    # 启动类
│   │       ├── controller/                    # 控制器层
│   │       ├── service/                       # 服务层
│   │       ├── mapper/                        # 数据访问层
│   │       ├── entity/                        # 实体类
│   │       ├── common/                        # 通用类
│   │       ├── exception/                     # 异常处理
│   │       └── config/                        # 配置类
│   └── src/main/resources/
│       └── application.yml                    # 配置文件
├── admin-service/            # 管理服务模块
│   ├── src/main/java/
│   │   └── com/yangtravel/admin/
│   │       ├── AdminServiceApplication.java   # 启动类
│   │       ├── controller/                    # 控制器层
│   │       ├── service/                       # 服务层
│   │       ├── mapper/                        # 数据访问层
│   │       ├── entity/                        # 实体类
│   │       ├── common/                        # 通用类
│   │       ├── exception/                     # 异常处理
│   │       └── config/                        # 配置类
│   └── src/main/resources/
│       └── application.yml                    # 配置文件
└── sql/
    └── init.sql                              # 数据库初始化脚本
```

## 数据库配置

### 数据库信息
- **服务器地址**: rm-uf6gb8bsu7t855li5qo.mysql.rds.aliyuncs.com:3306
- **数据库名称**: yangyangchuyou
- **用户名**: yangyangchuyou
- **密码**: Yangyang123@qwe

### 数据库表结构

#### 用户信息表 (user_info)
- id: 主键，自增
- username: 用户名，唯一
- nickname: 昵称
- phone: 手机号
- email: 邮箱
- avatar_url: 头像URL
- gender: 性别（0-未知，1-男，2-女）
- create_time: 创建时间
- update_time: 更新时间
- status: 状态（0-禁用，1-启用）

#### 管理员表 (admin)
- id: 主键，自增
- username: 用户名，唯一
- password: 密码
- nickname: 昵称
- create_time: 创建时间
- update_time: 更新时间
- status: 状态（0-禁用，1-启用）

## 服务端口

- **用户服务**: 8081
- **管理服务**: 8082

## 管理员账号

- **用户名**: admin
- **密码**: 1234@qwer

## 快速开始

### 1. 环境要求

- JDK 17+
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库初始化

执行 `sql/init.sql` 脚本初始化数据库表和基础数据：

```bash
mysql -h rm-uf6gb8bsu7t855li5qo.mysql.rds.aliyuncs.com -P 3306 -u yangyangchuyou -p yangyangchuyou < sql/init.sql
```

### 3. 编译项目

```bash
mvn clean compile
```

### 4. 启动服务

#### 启动用户服务
```bash
cd user-service
mvn spring-boot:run
```
或者
```bash
java -jar user-service/target/user-service-1.0.0.jar
```

#### 启动管理服务
```bash
cd admin-service
mvn spring-boot:run
```
或者
```bash
java -jar admin-service/target/admin-service-1.0.0.jar
```

## API文档

启动服务后，可以通过以下地址访问API文档：

- **用户服务API文档**: http://localhost:8081/swagger-ui.html
- **管理服务API文档**: http://localhost:8082/swagger-ui.html

## API接口说明

### 用户服务 (端口: 8081)

#### 用户管理接口
- `GET /api/users` - 查询用户列表（支持分页）
- `GET /api/users/{id}` - 根据ID查询用户
- `POST /api/users` - 新增用户
- `PUT /api/users/{id}` - 修改用户信息
- `DELETE /api/users/{id}` - 删除用户

### 管理服务 (端口: 8082)

#### 管理员接口
- `POST /api/admin/login` - 管理员登录
- `GET /api/admin/info` - 获取管理员信息

#### 用户管理接口
- `GET /api/admin/users` - 查询用户列表（支持分页）
- `GET /api/admin/users/{id}` - 根据ID查询用户
- `PUT /api/admin/users/{id}` - 修改用户信息
- `DELETE /api/admin/users/{id}` - 删除用户
- `PUT /api/admin/users/{id}/status` - 更新用户状态

## 测试说明

### 1. 用户服务测试

#### 创建用户
```bash
curl -X POST "http://localhost:8081/api/users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "nickname": "新用户",
    "phone": "13800138888",
    "email": "<EMAIL>",
    "gender": 1
  }'
```

#### 查询用户列表
```bash
curl "http://localhost:8081/api/users?current=1&size=10"
```

### 2. 管理服务测试

#### 管理员登录
```bash
curl -X POST "http://localhost:8082/api/admin/login" \
  -d "username=admin&password=1234@qwer"
```

#### 查询用户列表（需要先登录获取token）
```bash
curl "http://localhost:8082/api/admin/users?current=1&size=10" \
  -H "Authorization: mock-token-1"
```

## 开发说明

### 统一响应格式

所有API接口都使用统一的响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1691472000000
}
```

### 异常处理

项目集成了全局异常处理器，自动处理以下异常：
- 业务异常 (BusinessException)
- 参数校验异常
- 系统异常

### 自动填充

使用MyBatis Plus自动填充功能，自动设置创建时间和更新时间。

## 注意事项

1. 本项目为演示项目，密码未加密，生产环境请使用加密存储
2. Token验证采用简单模拟，生产环境建议使用JWT
3. 请确保数据库连接配置正确
4. 建议在生产环境中配置连接池和监控

## 联系方式

- 项目作者: YangTravel
- 邮箱: <EMAIL>
