@echo off
echo ========================================
echo YangTravel 微服务启动脚本
echo ========================================

echo.
echo 正在编译项目...
call mvn clean package -DskipTests

if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo.

echo 启动用户服务 (端口: 8081)...
start "User Service" cmd /k "cd user-service && mvn spring-boot:run"

echo 等待3秒...
timeout /t 3 /nobreak >nul

echo 启动管理服务 (端口: 8082)...
start "Admin Service" cmd /k "cd admin-service && mvn spring-boot:run"

echo.
echo ========================================
echo 服务启动完成！
echo.
echo 用户服务: http://localhost:8081
echo 用户服务API文档: http://localhost:8081/swagger-ui.html
echo.
echo 管理服务: http://localhost:8082  
echo 管理服务API文档: http://localhost:8082/swagger-ui.html
echo.
echo 管理员账号: admin / 1234@qwer
echo ========================================

pause
