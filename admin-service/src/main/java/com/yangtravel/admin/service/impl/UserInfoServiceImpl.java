package com.yangtravel.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yangtravel.admin.entity.UserInfo;
import com.yangtravel.admin.mapper.UserInfoMapper;
import com.yangtravel.admin.service.UserInfoService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 用户信息服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    @Override
    public IPage<UserInfo> getUserPage(Page<UserInfo> page, String username, Integer status) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据用户名模糊查询
        if (StringUtils.hasText(username)) {
            queryWrapper.like(UserInfo::getUsername, username);
        }
        
        // 根据状态查询
        if (status != null) {
            queryWrapper.eq(UserInfo::getStatus, status);
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(UserInfo::getCreateTime);
        
        return this.page(page, queryWrapper);
    }

    @Override
    public boolean updateUserStatus(Long id, Integer status) {
        if (id == null || status == null) {
            return false;
        }

        LambdaUpdateWrapper<UserInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserInfo::getId, id)
                    .set(UserInfo::getStatus, status);

        return this.update(updateWrapper);
    }
}
