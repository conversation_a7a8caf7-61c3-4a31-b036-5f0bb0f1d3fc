package com.yangtravel.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yangtravel.admin.entity.UserInfo;

/**
 * 用户信息服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface UserInfoService extends IService<UserInfo> {

    /**
     * 分页查询用户列表
     * 
     * @param page 分页参数
     * @param username 用户名（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    IPage<UserInfo> getUserPage(Page<UserInfo> page, String username, Integer status);

    /**
     * 更新用户状态
     * 
     * @param id 用户ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateUserStatus(Long id, Integer status);
}
