package com.yangtravel.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yangtravel.admin.entity.Admin;

/**
 * 管理员服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface AdminService extends IService<Admin> {

    /**
     * 管理员登录
     * 
     * @param username 用户名
     * @param password 密码
     * @return 管理员信息
     */
    Admin login(String username, String password);

    /**
     * 根据用户名查询管理员
     * 
     * @param username 用户名
     * @return 管理员信息
     */
    Admin getAdminByUsername(String username);
}
