package com.yangtravel.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yangtravel.admin.entity.Admin;
import com.yangtravel.admin.exception.BusinessException;
import com.yangtravel.admin.mapper.AdminMapper;
import com.yangtravel.admin.service.AdminService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 管理员服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements AdminService {

    @Override
    public Admin login(String username, String password) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            throw new BusinessException(400, "用户名和密码不能为空");
        }

        Admin admin = getAdminByUsername(username);
        if (admin == null) {
            throw new BusinessException(401, "用户名或密码错误");
        }

        if (admin.getStatus() == 0) {
            throw new BusinessException(403, "账号已被禁用");
        }

        // 简单的密码验证（实际项目中应该使用加密）
        if (!password.equals(admin.getPassword())) {
            throw new BusinessException(401, "用户名或密码错误");
        }

        // 不返回密码
        admin.setPassword(null);
        return admin;
    }

    @Override
    public Admin getAdminByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }

        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Admin::getUsername, username);

        return this.getOne(queryWrapper);
    }
}
