package com.yangtravel.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yangtravel.admin.common.Result;
import com.yangtravel.admin.entity.UserInfo;
import com.yangtravel.admin.exception.BusinessException;
import com.yangtravel.admin.service.UserInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@RestController
@RequestMapping("/api/admin/users")
@Tag(name = "用户管理", description = "管理员用户管理功能")
public class UserManageController {

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 查询用户列表（分页）
     */
    @GetMapping
    @Operation(summary = "查询用户列表", description = "管理员查询用户列表，支持分页和条件查询")
    public Result<IPage<UserInfo>> getUserList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam(required = false) Integer status) {
        
        Page<UserInfo> page = new Page<>(current, size);
        IPage<UserInfo> result = userInfoService.getUserPage(page, username, status);
        return Result.success(result);
    }

    /**
     * 根据ID查询用户
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询用户", description = "管理员根据用户ID查询用户详细信息")
    public Result<UserInfo> getUserById(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        
        UserInfo userInfo = userInfoService.getById(id);
        if (userInfo == null) {
            throw new BusinessException(404, "用户不存在");
        }
        return Result.success(userInfo);
    }

    /**
     * 修改用户信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "修改用户信息", description = "管理员修改用户信息")
    public Result<UserInfo> updateUser(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id,
            @Valid @RequestBody UserInfo userInfo) {
        
        // 检查用户是否存在
        UserInfo existingUser = userInfoService.getById(id);
        if (existingUser == null) {
            throw new BusinessException(404, "用户不存在");
        }
        
        userInfo.setId(id);
        boolean success = userInfoService.updateById(userInfo);
        if (!success) {
            throw new BusinessException("用户更新失败");
        }
        
        return Result.success("用户更新成功", userInfo);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "管理员删除用户")
    public Result<Void> deleteUser(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        
        // 检查用户是否存在
        UserInfo existingUser = userInfoService.getById(id);
        if (existingUser == null) {
            throw new BusinessException(404, "用户不存在");
        }
        
        boolean success = userInfoService.removeById(id);
        if (!success) {
            throw new BusinessException("用户删除失败");
        }
        
        return Result.success();
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新用户状态", description = "管理员启用或禁用用户")
    public Result<Void> updateUserStatus(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id,
            @Parameter(description = "状态：0-禁用，1-启用", required = true) @RequestParam Integer status) {
        
        // 检查用户是否存在
        UserInfo existingUser = userInfoService.getById(id);
        if (existingUser == null) {
            throw new BusinessException(404, "用户不存在");
        }
        
        // 验证状态值
        if (status != 0 && status != 1) {
            throw new BusinessException(400, "状态值无效，只能是0或1");
        }
        
        boolean success = userInfoService.updateUserStatus(id, status);
        if (!success) {
            throw new BusinessException("状态更新失败");
        }
        
        return Result.success();
    }
}
