package com.yangtravel.admin.controller;

import com.yangtravel.admin.common.Result;
import com.yangtravel.admin.entity.Admin;
import com.yangtravel.admin.service.AdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员控制器
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@RestController
@RequestMapping("/api/admin")
@Tag(name = "管理员管理", description = "管理员登录和管理功能")
public class AdminController {

    @Autowired
    private AdminService adminService;

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    @Operation(summary = "管理员登录", description = "管理员用户名密码登录")
    public Result<Map<String, Object>> login(
            @Parameter(description = "用户名", required = true) @RequestParam String username,
            @Parameter(description = "密码", required = true) @RequestParam String password) {
        
        Admin admin = adminService.login(username, password);
        
        Map<String, Object> result = new HashMap<>();
        result.put("admin", admin);
        result.put("token", "mock-token-" + admin.getId()); // 简单的token模拟
        
        return Result.success("登录成功", result);
    }

    /**
     * 获取管理员信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取管理员信息", description = "根据token获取当前管理员信息")
    public Result<Admin> getAdminInfo(
            @Parameter(description = "Token", required = true) @RequestHeader("Authorization") String token) {
        
        // 简单的token解析（实际项目中应该使用JWT等）
        if (token == null || !token.startsWith("mock-token-")) {
            return Result.error(401, "无效的token");
        }
        
        try {
            String idStr = token.replace("mock-token-", "");
            Long adminId = Long.parseLong(idStr);
            
            Admin admin = adminService.getById(adminId);
            if (admin == null) {
                return Result.error(401, "管理员不存在");
            }
            
            // 不返回密码
            admin.setPassword(null);
            return Result.success(admin);
        } catch (NumberFormatException e) {
            return Result.error(401, "无效的token");
        }
    }
}
